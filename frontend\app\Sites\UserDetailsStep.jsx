import React from 'react';
import { View, Text, TextInput, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { styles } from './siteStyles';

const UserDetailsStep = ({ theme, user, setStep }) => {
    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Your Details
            </Text>
            <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
                Are the details displayed correct? Update in profile settings if
                needed.
            </Text>

            {/* Name */}
            <View
                style={[
                    styles.inputContainer,
                    styles.disabledInputContainer,
                    {
                        backgroundColor: theme.ACCENT,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="person-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={user?.name || ''}
                    style={[
                        styles.input,
                        styles.disabledInput,
                        { color: theme.TEXT_PRIMARY },
                    ]}
                    editable={false}
                    placeholder="Name"
                    accessibilityLabel="User name"
                    testID="name-display"
                />
            </View>

            {/* Email */}
            <View
                style={[
                    styles.inputContainer,
                    styles.disabledInputContainer,
                    {
                        backgroundColor: theme.ACCENT,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="mail-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={user?.email || ''}
                    style={[
                        styles.input,
                        styles.disabledInput,
                        { color: theme.TEXT_PRIMARY },
                    ]}
                    editable={false}
                    placeholder="Email"
                    accessibilityLabel="User email"
                    testID="email-display"
                />
            </View>

            {/* Phone */}
            <View
                style={[
                    styles.inputContainer,
                    styles.disabledInputContainer,
                    {
                        backgroundColor: theme.ACCENT,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="call-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={user?.phone || ''}
                    style={[
                        styles.input,
                        styles.disabledInput,
                        { color: theme.TEXT_PRIMARY },
                    ]}
                    editable={false}
                    placeholder="Phone"
                    accessibilityLabel="User phone"
                    testID="phone-display"
                />
            </View>

            {/* Next Button */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={styles.submitButton}
                    onPress={() => setStep('siteDetails')}
                    accessibilityLabel="Proceed to site details"
                    accessibilityRole="button"
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.submitButtonGradient}
                    >
                        <Text
                            style={[
                                styles.submitButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            Next
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default UserDetailsStep;
